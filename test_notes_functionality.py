#!/usr/bin/env python
"""
Test script to verify the Notes functionality works correctly
"""
import os
import sys
import django
from datetime import datetime, time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'appradio.settings')
django.setup()

from apps.core.models import Presenter
from apps.shows.models import Show, ShowPresenter, PresenterShowNote
from apps.organizations.models import Organization

def test_notes_functionality():
    """Test the notes functionality"""
    print("Testing Notes functionality...")
    
    # Get or create test data
    try:
        # Get first organization
        org = Organization.objects.first()
        if not org:
            print("No organization found. Please create one first.")
            return
        
        # Get first presenter
        presenter = Presenter.objects.filter(organization=org).first()
        if not presenter:
            print("No presenter found. Please create one first.")
            return
        
        # Get or create a test show for today
        today = datetime.now().date()
        weekday = today.weekday()  # 0=Monday, 6=Sunday
        
        show, created = Show.objects.get_or_create(
            organization=org,
            name="Test Show for Notes",
            defaults={
                'description': 'Test show for notes functionality',
                'start_time': time(9, 0),
                'end_time': time(12, 0),
                'days_of_week': [weekday],  # Today's weekday
                'is_active': True
            }
        )
        
        if created:
            print(f"Created test show: {show.name}")
        else:
            print(f"Using existing show: {show.name}")
        
        # Assign presenter to show if not already assigned
        show_presenter, created = ShowPresenter.objects.get_or_create(
            show=show,
            presenter=presenter,
            defaults={
                'role': 'Host',
                'is_primary': True,
                'is_active': True
            }
        )
        
        if created:
            print(f"Assigned presenter {presenter.display_name} to show {show.name}")
        
        # Test creating a note
        note_content = f"Test note created at {datetime.now()}"
        note, created = PresenterShowNote.objects.get_or_create(
            presenter=presenter,
            show=show,
            date=today,
            defaults={'notes': note_content}
        )
        
        if created:
            print(f"Created note: {note_content}")
        else:
            # Update existing note
            note.notes = note_content
            note.save()
            print(f"Updated existing note: {note_content}")
        
        # Verify note was saved
        saved_note = PresenterShowNote.objects.get(
            presenter=presenter,
            show=show,
            date=today
        )
        
        print(f"Verified note saved: {saved_note.notes}")
        print(f"Note ID: {saved_note.id}")
        print(f"Show duration: {show.duration_minutes} minutes")
        print(f"Show time: {show.start_time} - {show.end_time}")
        
        print("\n✅ Notes functionality test completed successfully!")
        print(f"You can now test the dashboard at: http://127.0.0.1:8000/presenters/{presenter.id}/dashboard/")
        
    except Exception as e:
        print(f"❌ Error testing notes functionality: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_notes_functionality()
